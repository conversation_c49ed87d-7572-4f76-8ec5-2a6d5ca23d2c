//
//  LoginViewController2.0.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/10.
//

import UIKit
import SnapKit
import Combine
import IQKeyboardManagerSwift

class LoginViewController2_0: BaseViewController {
    // MARK: - Delegate (AuthCoordinator)
    weak var delegate: NormalLoginDelegate?
    // MARK: - ViewModel
    private let viewModel = LoginViewModel()
    private var cancellables = Set<AnyCancellable>()
    // 新增：首次登录标记
    var isFirstTimeLogin: Bool = false
    
    // MARK: - 背景图
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "login_bg")
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.isUserInteractionEnabled = false
        return imageView
    }()
    
    // MARK: - 顶部UI
    private lazy var logoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "login_logo")
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 8
        imageView.clipsToBounds = true
        return imageView
    }()
    private lazy var welcomeLabel: UILabel = {
        let label = UILabel()
        label.text = "Hello!"
        label.font = UIFont.systemFont(ofSize: 36 * CGFloat(printScreenHeightRatio()), weight: .medium)
        label.textColor = UIColor(hex: "#361F10")
        return label
    }()
    private lazy var descLabel: UILabel = {
        let label = UILabel()
        label.text = "一周有7天 天天都上树小柒"
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#361F10", alpha: 0.6)
        return label
    }()
    private lazy var skipButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("暂不登录", for: .normal)
        button.setTitleColor(UIColor(hex: "#361F10", alpha: 0.6), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.addTarget(self, action: #selector(skipButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - 登录表单容器
    private lazy var formContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 11
        view.layer.masksToBounds = true
        view.alpha = 0.98
        return view
    }()
    // 登录模式切换
    private lazy var modeSegment: UISegmentedControl = {
        let seg = UISegmentedControl(items: ["密码登录", "验证码登录"])
        seg.selectedSegmentIndex = 0
        return seg
    }()
    private lazy var captchaImageView: UIImageView = {
        let img = UIImageView()
        img.backgroundColor = UIColor(hex: "#F7F7F7")
        img.contentMode = .scaleAspectFit
        img.layer.cornerRadius = 8
        img.clipsToBounds = true
        img.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(refreshCaptcha))
        img.addGestureRecognizer(tapGesture)
        return img
    }()
    
    private lazy var captchaRefreshIcon: UIImageView = {
        let img = UIImageView()
        img.image = UIImage(systemName: "arrow.clockwise")
        img.tintColor = UIColor(hex: "#FF8F1F")
        img.contentMode = .scaleAspectFit
        img.alpha = 0.7
        return img
    }()
    
    private lazy var captchaLoadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        indicator.color = UIColor(hex: "#FF8F1F")
        return indicator
    }()
    
    private lazy var getCodeButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("获取验证码", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = UIColor(hex: "#FF8F1F")
        btn.layer.cornerRadius = 12
        btn.titleLabel?.font = UIFont.monospacedDigitSystemFont(ofSize: 14, weight: .medium)
        return btn
    }()
    private lazy var forgotPasswordButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("忘记密码？", for: .normal)
        btn.setTitleColor(UIColor(hex: "#0256FF"), for: .normal)
        btn.titleLabel?.font = .systemFont(ofSize: 12)
        btn.addTarget(self, action: #selector(forgotPasswordTapped), for: .touchUpInside)
        return btn
    }()
    // 主登录按钮
    private lazy var loginButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("登录", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.backgroundColor = UIColor(hex: "#FF8F1F")
        btn.layer.cornerRadius = 25
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        return btn
    }()
    // 协议勾选
    private lazy var agreementCheckButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "app_radio_Default"), for: .normal)
        btn.setImage(UIImage(named: "app_radio_select"), for: .selected)
        return btn
    }()
    // 协议标签
    private lazy var agreementLabel: UILabel = {
        let label = UILabel()
        let text = "登录即表示同意《用户协议》和《隐私协议》"
        let attributedString = NSMutableAttributedString(string: text)
        let baseAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 12),
            .foregroundColor: UIColor(hex: "#000000", alpha: 0.45)
        ]
        attributedString.addAttributes(baseAttributes, range: NSRange(location: 0, length: text.count))
        let userAgreementRange = (text as NSString).range(of: "《用户协议》")
        let privacyRange = (text as NSString).range(of: "《隐私协议》")
        let linkAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor(hex: "#FA741A"),
            .font: UIFont.systemFont(ofSize: 12)
        ]
        attributedString.addAttributes(linkAttributes, range: userAgreementRange)
        attributedString.addAttributes(linkAttributes, range: privacyRange)
        label.attributedText = attributedString
        label.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(agreementLabelTapped(_:)))
        label.addGestureRecognizer(tap)
        return label
    }()
    // 其他登录方式
    private lazy var otherLoginLabel: UILabel = {
        let label = UILabel()
        label.text = "其他登录方式"
        label.textColor = UIColor(hex: "#000000", alpha: 0.45)
        label.font = UIFont.systemFont(ofSize: 14)
        label.textAlignment = .center
        return label
    }()
    private lazy var socialStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .equalSpacing
        stack.alignment = .center
        stack.spacing = 30
        return stack
    }()
    private lazy var wechatButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "icon_wechat_login"), for: .normal)
        btn.addTarget(self, action: #selector(wechatLogin), for: .touchUpInside)
        return btn
    }()
    private lazy var appleButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "icon_apple_login"), for: .normal)
        return btn
    }()

    // MARK: - 切换背景栏
    private lazy var switchBgBar: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        return v
    }()
    private lazy var leftSwitchButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("密码登录", for: .normal)
        btn.setTitleColor(UIColor(hex: "#361F10"), for: .normal)
        btn.backgroundColor = .clear
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        btn.addTarget(self, action: #selector(switchToLeftBg), for: .touchUpInside)
        return btn
    }()
    private lazy var rightSwitchButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("验证码登录", for: .normal)
        btn.setTitleColor(UIColor(hex: "#361F10"), for: .normal)
        btn.backgroundColor = .clear
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        btn.addTarget(self, action: #selector(switchToRightBg), for: .touchUpInside)
        return btn
    }()
    private lazy var formBgImageView: UIImageView = {
        let img = UIImageView()
        img.image = UIImage(named: "login_sw_type_left_bg")
        img.contentMode = .scaleAspectFill
        img.clipsToBounds = true
        return img
    }()

    // 登录模式
    private enum LoginMode {
        case password
        case smsCode
    }
    private var currentLoginMode: LoginMode = .password

    // 输入区容器（密码登录）
    private lazy var formFieldsView_pwd: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 16
        return v
    }()
    // 输入区容器（验证码登录）
    private lazy var formFieldsView_code: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 16
        return v
    }()
    private var formFieldsPwdLeftConstraint: Constraint? = nil
    private var formFieldsCodeLeftConstraint: Constraint? = nil
    private var loginButtonTopConstraint: Constraint?

    // MARK: - 密码登录UI
    private lazy var phoneTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "手机号"
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(hex: "#361F10")
        return label
    }()
    private lazy var passwordTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "密码"
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(hex: "#361F10")
        return label
    }()
    // 验证码登录专用"密码登录"按钮
    private lazy var codeSwitchToPwdButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("一键登录", for: .normal)
        btn.setTitleColor(UIColor(hex: "#361F10", alpha: 0.6), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        btn.contentHorizontalAlignment = .left
        btn.addTarget(self, action: #selector(oneClickLoginTapped), for: .touchUpInside)
        btn.isHidden = true // 默认隐藏，检查是否支持后再显示
        return btn
    }()

    // 密码登录专用"验证码登录"按钮
    private lazy var pwdSwitchToSmsButton: UIButton = {
        let btn = UIButton(type: .system)
        btn.setTitle("一键登录", for: .normal)
        btn.setTitleColor(UIColor(hex: "#361F10", alpha: 0.6), for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        btn.contentHorizontalAlignment = .left
        btn.addTarget(self, action: #selector(oneClickLoginTapped), for: .touchUpInside)
        btn.isHidden = true // 默认隐藏，检查是否支持后再显示
        return btn
    }()

    // 图形验证码title
    private lazy var captchaTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "图形验证码"
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(hex: "#361F10")
        return label
    }()
    // 验证码title
    private lazy var codeTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "验证码"
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(hex: "#361F10")
        return label
    }()

    // 验证码登录专用手机号title
    private lazy var phoneTitleLabel_code: UILabel = {
        let label = UILabel()
        label.text = "手机号"
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(hex: "#361F10")
        return label
    }()
    // 验证码登录专用手机号输入框
    private lazy var phoneTextField_code: UITextField = {
        let tf = createBorderedTextField(placeholder: "请输入手机号")
        tf.keyboardType = .numberPad
        return tf
    }()

    // 验证码登录专用图形验证码输入框
    private lazy var captchaTextField_code: UITextField = {
        let tf = createBorderedTextField(placeholder: "请输入图形验证码")
        tf.keyboardType = .webSearch
        return tf
    }()
    // 验证码登录专用验证码输入框
    private lazy var codeTextField_code: UITextField = {
        let tf = createBorderedTextField(placeholder: "请输入验证码")
        tf.keyboardType = .numberPad
        return tf
    }()

    // 自定义带边框输入框
    private func createBorderedTextField(placeholder: String) -> UITextField {
        let tf = UITextField()
        tf.placeholder = placeholder
        tf.font = UIFont.systemFont(ofSize: 14)
        tf.backgroundColor = .white
        tf.layer.cornerRadius = 12
        tf.layer.borderWidth = 1
        tf.layer.borderColor = UIColor(hex: "#F5F2F0").cgColor
        tf.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 44))
        tf.leftViewMode = .always
        return tf
    }

    // 替换原有phoneTextField和passwordTextField
    private lazy var phoneTextField_pwd: UITextField = {
        let tf = createBorderedTextField(placeholder: "请输入手机号")
        tf.keyboardType = .numberPad
        return tf
    }()
    private lazy var passwordTextField_pwd: UITextField = {
        let tf = createBorderedTextField(placeholder: "请输入密码")
        tf.isSecureTextEntry = true
        return tf
    }()

    // MARK: - 底部弹出视图
    private lazy var bottomSheetView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.isHidden = true
        return view
    }()
    
    private lazy var bottomSheetStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .equalSpacing
        stack.alignment = .center
        stack.spacing = 30
        return stack
    }()
    
    private lazy var bottomSheetWechatButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "icon_wechat_login"), for: .normal)
        return btn
    }()
    
    private lazy var bottomSheetAppleButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "icon_apple_login"), for: .normal)
        return btn
    }()
    
    private lazy var bottomSheetTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "其他登录方式"
        label.textColor = UIColor(hex: "#000000", alpha: 0.45)
        label.font = UIFont.systemFont(ofSize: 14)
        label.textAlignment = .center
        return label
    }()
    
    private var bottomSheetHeight: CGFloat {
        return 124 + WindowUtil.safeAreaBottom
    }
    
    private func setupBottomSheet() {
        view.addSubview(bottomSheetView)
        bottomSheetView.addSubview(bottomSheetTitleLabel)
        bottomSheetView.addSubview(bottomSheetStack)
        
        bottomSheetStack.addArrangedSubview(bottomSheetWechatButton)
        bottomSheetStack.addArrangedSubview(bottomSheetAppleButton)
        
        bottomSheetView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(bottomSheetHeight)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(bottomSheetHeight)
        }
        
        bottomSheetTitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(16)
        }
        
        bottomSheetStack.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(bottomSheetTitleLabel.snp.bottom).offset(8)
            make.height.equalTo(64)
        }
        
        [bottomSheetWechatButton, bottomSheetAppleButton].forEach { btn in
            btn.snp.makeConstraints { make in
                make.width.height.equalTo(64)
            }
        }
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(otherLoginLabelTapped))
        otherLoginLabel.addGestureRecognizer(tapGesture)
        otherLoginLabel.isUserInteractionEnabled = true
        
        // 添加点击空白处关闭的手势
        let tapToDismiss = UITapGestureRecognizer(target: self, action: #selector(handleTapToDismiss))
        view.addGestureRecognizer(tapToDismiss)
    }
    
    @objc private func otherLoginLabelTapped() {
        if socialStack.isHidden {
            showBottomSheet()
        }
    }
    
    private func showBottomSheet() {
        bottomSheetView.isHidden = false
        UIView.animate(withDuration: 0.3) {
            self.bottomSheetView.snp.updateConstraints { make in
                make.bottom.equalTo(self.view.safeAreaLayoutGuide.snp.bottom)
            }
            self.view.layoutIfNeeded()
        }
    }
    
    private func hideBottomSheet() {
        UIView.animate(withDuration: 0.3, animations: {
            self.bottomSheetView.snp.updateConstraints { make in
                make.bottom.equalTo(self.view.safeAreaLayoutGuide.snp.bottom).offset(self.bottomSheetHeight)
            }
            self.view.layoutIfNeeded()
        }) { _ in
            self.bottomSheetView.isHidden = true
        }
    }

    @objc private func handleTapToDismiss(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !bottomSheetView.frame.contains(location) && !bottomSheetView.isHidden {
            hideBottomSheet()
        }
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        showNavBar = false
        setupUI()
        setupConstraints()
        setupBottomSheet()
        printScreenHeightRatio()
        bindViewModel()
        print("IQK resignOnTouchOutside:", IQKeyboardManager.shared.resignOnTouchOutside)
        // 初始加载验证码
        refreshCaptcha()
        setupTapToDismissKeyboard()
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWXCode(_:)),
            name: .wxLoginCodeReady,
            object: nil
        )
        // 监听首次登录完成通知，弹出兴趣选择弹窗
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFirstTimeLoginCompleted),
            name: .firstTimeLoginCompleted,
            object: nil
        )
        // 检查是否支持一键登录
        checkOneClickLoginSupport()
        // 默认进入短信验证码登录模式
        switchToRightBg()
    }
    
    enum LoginToken {
        case phone(String)         // 手机验证码接口返回的 token/id
        case wechat(String)        // wechat code
    }
    
    @objc private func handleWXCode(_ n: Notification) {
        guard let code = n.userInfo?["code"] as? String else { return }
        viewModel.loginWithWeChat(code: code)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    func continueUnifiedLogin(with token: LoginToken) {
        switch token {
        case .wechat(let cod):
            print("回传的微信code：\(cod)")
            
        case .phone(_):
            print("回传的是手机token")
        }
        
    }
    
    private func setupUI() {
        view.addSubview(backgroundImageView)
        view.addSubview(logoImageView)
        view.addSubview(welcomeLabel)
        view.addSubview(descLabel)
        view.addSubview(skipButton)
        view.addSubview(formContainer)
        formContainer.addSubview(formBgImageView)
        formContainer.addSubview(switchBgBar)
        switchBgBar.addSubview(leftSwitchButton)
        switchBgBar.addSubview(rightSwitchButton)
        formContainer.addSubview(formFieldsView_pwd)
        formContainer.addSubview(formFieldsView_code)
        // 密码登录UI
        formFieldsView_pwd.addSubview(phoneTitleLabel)
        formFieldsView_pwd.addSubview(phoneTextField_pwd)
        formFieldsView_pwd.addSubview(passwordTitleLabel)
        formFieldsView_pwd.addSubview(passwordTextField_pwd)
        formFieldsView_pwd.addSubview(pwdSwitchToSmsButton)
        formFieldsView_pwd.addSubview(forgotPasswordButton)
        // 验证码登录UI
        formFieldsView_code.addSubview(phoneTitleLabel_code)
        formFieldsView_code.addSubview(phoneTextField_code)
        formFieldsView_code.addSubview(captchaTitleLabel)
        formFieldsView_code.addSubview(captchaTextField_code)
        formFieldsView_code.addSubview(captchaImageView)
        captchaImageView.addSubview(captchaRefreshIcon)
        formFieldsView_code.addSubview(captchaLoadingIndicator)
        formFieldsView_code.addSubview(codeTitleLabel)
        formFieldsView_code.addSubview(codeTextField_code)
        formFieldsView_code.addSubview(getCodeButton)
        formFieldsView_code.addSubview(codeSwitchToPwdButton)
        formContainer.addSubview(loginButton)
        formContainer.addSubview(agreementCheckButton)
        formContainer.addSubview(agreementLabel)
        formContainer.addSubview(otherLoginLabel)
        formContainer.addSubview(socialStack)
        socialStack.addArrangedSubview(wechatButton)
        socialStack.addArrangedSubview(appleButton)
    }
    
    private func setupConstraints() {
        let screenW = UIScreen.main.bounds.width
        let screenH = UIScreen.main.bounds.height
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        skipButton.snp.makeConstraints { make in
            make.top.equalTo(WindowUtil.safeAreaTop)
            make.right.equalToSuperview().offset(-12)
            make.height.equalTo(44)
        }
        logoImageView.snp.makeConstraints { make in
            make.top.equalTo(skipButton.snp.bottom).offset(13 * printScreenHeightRatio())
//            make.right.equalTo(skipButton.snp.right)
            make.left.equalToSuperview().offset(26)
            make.width.height.equalTo(100 * printScreenHeightRatio())
        }
        welcomeLabel.snp.makeConstraints { make in
            make.top.equalTo(logoImageView.snp.top).offset(18 * printScreenHeightRatio())
//            make.right.equalToSuperview().offset(self.view.bounds.width * 0.18)
            make.left.equalTo(logoImageView.snp.right).offset(self.view.bounds.width * 0.15)
        }
        descLabel.snp.makeConstraints { make in
            make.centerX.equalTo(welcomeLabel).offset(23)
            make.top.equalTo(welcomeLabel.snp.bottom).offset(11 * printScreenHeightRatio())
        }
        formContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.top.equalTo(logoImageView.snp.bottom).offset(17)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-10)
        }
        formBgImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        switchBgBar.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        leftSwitchButton.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalTo(switchBgBar.snp.width).multipliedBy(0.5)
        }
        rightSwitchButton.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.width.equalTo(switchBgBar.snp.width).multipliedBy(0.5)
        }
        // formFieldsView_pwd 约束
        formFieldsView_pwd.snp.makeConstraints { make in
            self.formFieldsPwdLeftConstraint = make.left.equalToSuperview().constraint
            make.top.equalTo(switchBgBar.snp.bottom)
            make.width.equalToSuperview()
            make.bottom.equalTo(pwdSwitchToSmsButton.snp_bottomMargin).offset(12)
        }
        // formFieldsView_code 约束（初始在右侧）
        formFieldsView_code.snp.makeConstraints { make in
            self.formFieldsCodeLeftConstraint = make.left.equalToSuperview().offset(screenW).constraint
            make.top.equalTo(switchBgBar.snp.bottom)
            make.width.equalToSuperview()
            make.bottom.equalTo(codeSwitchToPwdButton.snp_bottomMargin).offset(12)
        }
        // 密码登录UI布局
        phoneTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(36)
            make.top.equalToSuperview().offset(40 * printScreenHeightRatio())
        }
        phoneTextField_pwd.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(36)
            make.top.equalTo(phoneTitleLabel.snp.bottom).offset(8)
            make.height.equalTo(44)
        }
        passwordTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(36)
            make.top.equalTo(phoneTextField_pwd.snp.bottom).offset(20)
        }
        passwordTextField_pwd.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(36)
            make.top.equalTo(passwordTitleLabel.snp.bottom).offset(8)
            make.height.equalTo(44)
        }
        pwdSwitchToSmsButton.snp.makeConstraints { make in
            make.left.equalTo(passwordTextField_pwd)
            make.top.equalTo(passwordTextField_pwd.snp.bottom).offset(8)
            make.height.equalTo(20)
            make.width.greaterThanOrEqualTo(80)
        }
        forgotPasswordButton.snp.makeConstraints { make in
            make.right.equalTo(passwordTextField_pwd.snp.right)
            make.centerY.equalTo(pwdSwitchToSmsButton)
        }
        // 验证码登录UI布局
        phoneTitleLabel_code.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(36)
            make.top.equalToSuperview().offset(40 * printScreenHeightRatio())
        }
        phoneTextField_code.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(36)
            make.top.equalTo(phoneTitleLabel_code.snp.bottom).offset(8)
            make.height.equalTo(44)
        }
        captchaTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(36)
            make.top.equalTo(phoneTextField_code.snp.bottom).offset(10)
        }
        
        captchaTextField_code.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(36)
            make.top.equalTo(captchaTitleLabel.snp.bottom).offset(10)
            make.height.equalTo(44)
        }
        captchaImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-44)
            make.centerY.equalTo(captchaTextField_code)
            make.width.equalTo(90)
            make.height.equalTo(30)
        }
        
        captchaRefreshIcon.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview().inset(2)
            make.width.height.equalTo(12)
        }
        
        captchaLoadingIndicator.snp.makeConstraints { make in
            make.center.equalTo(captchaImageView)
        }
        
        codeTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(36)
            make.top.equalTo(captchaTextField_code.snp.bottom).offset(10)
        }
        
        codeTextField_code.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(36)
            make.top.equalTo(codeTitleLabel.snp.bottom).offset(10)
            make.height.equalTo(44)
        }
        getCodeButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-44)
            make.centerY.equalTo(codeTextField_code)
            make.width.equalTo(80)
            make.height.equalTo(26)
        }
        codeSwitchToPwdButton.snp.makeConstraints { make in
            make.left.equalTo(codeTextField_code)
            make.top.equalTo(codeTextField_code.snp.bottom).offset(10)
            make.height.equalTo(28)
            make.width.greaterThanOrEqualTo(80)
        }
        // 登录按钮 top 约束
        loginButton.snp.makeConstraints { make in
            self.loginButtonTopConstraint = make.top.equalTo(switchBgBar.snp.bottom).offset(281).constraint // 默认密码模式
            make.left.right.equalToSuperview().inset(32)
            make.height.equalTo(50)
        }
        
        agreementLabel.snp.makeConstraints { make in
            make.top.equalTo(loginButton.snp.bottom).offset(14)
            make.centerX.equalToSuperview().offset(12)
        }
        agreementCheckButton.snp.makeConstraints { make in
            make.right.equalTo(agreementLabel.snp.left).offset(-5)
            make.centerY.equalTo(agreementLabel)
            make.width.height.equalTo(18)
        }
        
        otherLoginLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-90)
        }
        socialStack.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(otherLoginLabel.snp.bottom).offset(8)
            make.height.equalTo(64)
        }
        [wechatButton, appleButton].forEach { btn in
            btn.snp.makeConstraints { make in
                make.width.height.equalTo(64)
            }
        }
    }

    @objc private func skipButtonTapped() {
        //点击了暂不登录
        NSObject.cancelPreviousPerformRequests(withTarget: self)
//        isViewDismissing = true
        dismiss(animated: true)
        
    }

    // 切换登录模式时调用
    private func updateFormForMode(_ mode: LoginMode) {
        currentLoginMode = mode

        // ⚠️ 同步 ViewModel 的登录模式，确保校验逻辑正确
        // LoginViewModel.LoginMode (全局) 与本控制器内部 LoginMode 同名，为避免冲突使用完全限定名
        let targetMode: Shuxiaoqi.LoginMode = (mode == .password) ? .password : .smsCode
        if viewModel.loginMode != targetMode {
            viewModel.loginMode = targetMode
        }

        let screenW = UIScreen.main.bounds.width
        switch mode {
        case .password:
            formFieldsPwdLeftConstraint?.update(offset: 0)
            formFieldsCodeLeftConstraint?.update(offset: screenW)
            loginButtonTopConstraint?.update(offset: 281)
        case .smsCode:
            formFieldsPwdLeftConstraint?.update(offset: -screenW)
            formFieldsCodeLeftConstraint?.update(offset: 0)
            loginButtonTopConstraint?.update(offset: 331)
        }
        UIView.animate(withDuration: 0.3) {
            self.formContainer.layoutIfNeeded()
        }
    }

    @objc private func switchToLeftBg() {
        formBgImageView.image = UIImage(named: "login_sw_type_left_bg")
        updateFormForMode(.password)
    }
    @objc private func switchToRightBg() {
        formBgImageView.image = UIImage(named: "login_sw_type_right_bg")
        updateFormForMode(.smsCode)
    }
    
    @objc private func agreementLabelTapped(_ gesture: UITapGestureRecognizer) {
        let label = gesture.view as! UILabel
        let text = label.attributedText!.string
        let point = gesture.location(in: label)
        if let textPosition = label.characterIndex(at: point) {
            // 调用导航方法
            if (text as NSString).range(of: "《用户协议》").contains(textPosition) {
                navigateToUserAgreement()
                return
            }
            if (text as NSString).range(of: "《隐私协议》").contains(textPosition) {
                navigateToPrivacyPolicy()
                return
            }
        }
    }

    func printScreenHeightRatio(designHeight: CGFloat = 850)->Float {
        let screenHeight = UIScreen.main.bounds.height
        let ratio = screenHeight / designHeight
        print("屏幕高度：\(screenHeight)，设计图高度：\(designHeight)，比例：\(ratio)")
        
        // 延迟打印距离，确保布局完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.printLabelsDistance()
        }
        
        if ratio > 1.0 {
            return 1.0
        }
        return Float(ratio)
    }
    
    // MARK: - 导航方法 (新增)
    private func navigateToUserAgreement() {
        print("导航到用户协议")
        guard let url = URL(string: "https://gzyoushu.com/privacy/ysh-docuser.htm") else { return }
        let webView = WebViewController(url: url, title: " ")
        print("无法找到 NavigationController，尝试 present WebViewController")
        webView.modalPresentationStyle = .fullScreen // 或者 .automatic
        self.present(webView, animated: true, completion: nil)
    }
    
    private func navigateToPrivacyPolicy() {
        print("导航到隐私政策")
        guard let url = URL(string: "https://gzyoushu.com/privacy/ys-doctset.html") else { return }
        let webView = WebViewController(url: url, title: " ")
        print("无法找到 NavigationController，尝试 present WebViewController")
        webView.modalPresentationStyle = .fullScreen // 或者 .automatic
        self.present(webView, animated: true, completion: nil)
    }
    
    private func printLabelsDistance() {
        let agreementBottom = agreementLabel.frame.maxY
        let otherLoginTop = otherLoginLabel.frame.minY
        let distance = otherLoginTop - agreementBottom
        print("otherLoginLabel\(otherLoginTop) 与 agreementLabel\(agreementBottom) 底部距离：\(distance)pt")
        if distance < 10 {
            print("距离过近")
//            otherLoginLabel.isHidden = true
            otherLoginLabel.snp.updateConstraints { make in
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-10)
            }
            socialStack.isHidden = true
        } else {
            otherLoginLabel.snp.updateConstraints { make in
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-90)
            }
            socialStack.isHidden = false
        }
    }

    // MARK: - 绑定ViewModel
    private func bindViewModel() {
        // 输入框绑定
        phoneTextField_pwd.addTarget(self, action: #selector(phoneTextFieldChanged), for: .editingChanged)
        passwordTextField_pwd.addTarget(self, action: #selector(passwordTextFieldChanged), for: .editingChanged)
        phoneTextField_code.addTarget(self, action: #selector(phoneTextFieldCodeChanged), for: .editingChanged)
        captchaTextField_code.addTarget(self, action: #selector(captchaTextFieldChanged), for: .editingChanged)
        codeTextField_code.addTarget(self, action: #selector(codeTextFieldChanged), for: .editingChanged)
        agreementCheckButton.addTarget(self, action: #selector(agreementCheckTapped), for: .touchUpInside)
        loginButton.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        getCodeButton.addTarget(self, action: #selector(getCodeButtonTapped), for: .touchUpInside)
        leftSwitchButton.addTarget(self, action: #selector(switchToLeftBg), for: .touchUpInside)
        rightSwitchButton.addTarget(self, action: #selector(switchToRightBg), for: .touchUpInside)
        
        // 移除切换登录方式的绑定
        // pwdSwitchToSmsButton.addTarget(self, action: #selector(switchToRightBg), for: .touchUpInside)
        // codeSwitchToPwdButton.addTarget(self, action: #selector(switchToLeftBg), for: .touchUpInside)

        // ViewModel -> UI
        viewModel.$isLoginButtonEnabled
            .receive(on: RunLoop.main)
            .sink { [weak self] enabled in
                self?.loginButton.isEnabled = enabled
            }.store(in: &cancellables)
        viewModel.$isLoading
            .receive(on: RunLoop.main)
            .sink { [weak self] loading in
                self?.loginButton.setTitle(loading ? "登录中..." : "登录", for: .normal)
            }.store(in: &cancellables)
        viewModel.$isCodeButtonEnabled
            .receive(on: RunLoop.main)
            .sink { [weak self] enabled in
                self?.getCodeButton.isEnabled = enabled
                self?.getCodeButton.backgroundColor = enabled ? UIColor(hex: "#FF8F1F") : UIColor(hex: "#CCCCCC")
            }.store(in: &cancellables)
        viewModel.$codeButtonTitle
            .receive(on: RunLoop.main)
            .sink { [weak self] title in
                guard let btn = self?.getCodeButton else { return }
                // 关闭动画，防止文字闪动
                UIView.performWithoutAnimation {
                    btn.setTitle(title, for: .normal)
                    btn.setTitle(title, for: .disabled)
                    // 立即布局，避免跳动
                    btn.layoutIfNeeded()
                }
            }.store(in: &cancellables)
        viewModel.$isAgreementChecked
            .receive(on: RunLoop.main)
            .sink { [weak self] checked in
                self?.agreementCheckButton.isSelected = checked
            }.store(in: &cancellables)
        viewModel.$loginMode
            .receive(on: RunLoop.main)
            .sink { [weak self] mode in
                self?.updateFormForMode(mode == .password ? .password : .smsCode)
            }.store(in: &cancellables)
        viewModel.$errorMessage
            .receive(on: RunLoop.main)
            .sink { [weak self] msg in
                if let msg = msg, !msg.isEmpty {
                    self?.showToast(msg)
                    self?.viewModel.resetError()
                }
            }.store(in: &cancellables)
        viewModel.$showAgreementAlert
            .receive(on: RunLoop.main)
            .sink { [weak self] show in
                if show { self?.showAgreementConfirmAlert() }
            }.store(in: &cancellables)
        viewModel.$loginSuccess
            .filter { $0 }
            .receive(on: RunLoop.main)
            .sink { [weak self] _ in
                self?.showToast("登录成功") { [weak self] in self?.dismiss(animated: true) }
            }.store(in: &cancellables)
        viewModel.$showBindPhone
            .compactMap { $0 }
            .receive(on: RunLoop.main)
            .sink { [weak self] bindPhoneId in
                self?.navigateToBindPhone(bindPhoneId: bindPhoneId)
            }.store(in: &cancellables)

        viewModel.$showSetPassword
            .compactMap { $0 }
            .receive(on: RunLoop.main)
            .sink { [weak self] tokenPhoneCheckId in
                self?.navigateToSetPassword(token: tokenPhoneCheckId.token, phone: tokenPhoneCheckId.phone, checkId: tokenPhoneCheckId.checkId)
            }.store(in: &cancellables)

        viewModel.$showPreferenceTags
            .filter { $0 }
            .receive(on: RunLoop.main)
            .sink { [weak self] _ in
                print("LoginViewController2.0: 收到 showPreferenceTags = true，准备显示兴趣选择页面")
                self?.showPreferenceTagsViewController()
            }.store(in: &cancellables)

        // 图形验证码绑定
        viewModel.$captchaImage
            .receive(on: RunLoop.main)
            .sink { [weak self] image in
                self?.captchaImageView.image = image
            }.store(in: &cancellables)
        
        viewModel.$isCaptchaLoading
            .receive(on: RunLoop.main)
            .sink { [weak self] isLoading in
                self?.captchaImageView.backgroundColor = isLoading ? UIColor(hex: "#CCCCCC") : UIColor(hex: "#F7F7F7")
                self?.captchaRefreshIcon.isHidden = isLoading
                if isLoading {
                    self?.captchaLoadingIndicator.startAnimating()
                } else {
                    self?.captchaLoadingIndicator.stopAnimating()
                }
            }.store(in: &cancellables)
    }

    // MARK: - 输入事件
    @objc private func phoneTextFieldChanged() {
        viewModel.phone = phoneTextField_pwd.text ?? ""
    }
    @objc private func passwordTextFieldChanged() {
        viewModel.password = passwordTextField_pwd.text ?? ""
    }
    @objc private func phoneTextFieldCodeChanged() {
        viewModel.phone = phoneTextField_code.text ?? ""
    }
    @objc private func captchaTextFieldChanged() {
        viewModel.captcha = captchaTextField_code.text ?? ""
    }
    @objc private func codeTextFieldChanged() {
        viewModel.code = codeTextField_code.text ?? ""
    }
    @objc private func agreementCheckTapped() {
        viewModel.isAgreementChecked.toggle()
    }
    @objc private func loginButtonTapped() {
        view.endEditing(true)
        viewModel.login()
    }
    @objc private func getCodeButtonTapped() {
        view.endEditing(true)
        viewModel.getCode()
    }
    // MARK: - 协议弹窗
    private func showAgreementConfirmAlert() {
        let alertController = UIAlertController(
            title: "温馨提示",
            message: "您需要同意用户协议和隐私协议才能继续",
            preferredStyle: .alert
        )
        let agreeAction = UIAlertAction(title: "同意并继续", style: .default) { [weak self] _ in
            self?.viewModel.agreementConfirmed()
            self?.viewModel.login()
        }
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        alertController.addAction(agreeAction)
        alertController.addAction(cancelAction)
        present(alertController, animated: true)
    }

    @objc private func refreshCaptcha() {
        viewModel.refreshCaptcha()
    }

    private func setupTapToDismissKeyboard() {
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tap.cancelsTouchesInView = false // 保证按钮等控件还能响应点击
        tap.delegate = self
        view.addGestureRecognizer(tap)
    }

    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }

    @objc private func wechatLogin() {
        guard WXApi.isWXAppInstalled() else {
            self.showToast("请先安装微信客户端")
            return
        }
        let req = SendAuthReq()
        req.scope = "snsapi_userinfo"
        req.state = UUID().uuidString
        WXApi.send(req)
    }

    // 添加导航到绑定手机页面的方法
    private func navigateToBindPhone(bindPhoneId: String) {
        print("需要绑定手机，bindPhoneId: \(bindPhoneId)")
        // 创建绑定手机页面并导航
        let bindPhoneVC = PhoneBindingViewController()
        bindPhoneVC.bindPhoneId = bindPhoneId
        bindPhoneVC.modalPresentationStyle = .fullScreen
        // 传递首次登录标记
        bindPhoneVC.isFirstTimeLogin = true
        present(bindPhoneVC, animated: true, completion: nil)

        // 监听绑定完成通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFirstTimeLoginCompleted),
            name: .firstTimeLoginCompleted,
            object: nil
        )
    }

    // MARK: - 忘记密码处理
    @objc private func forgotPasswordTapped() {
        print("忘记密码按钮点击")

        // 获取当前输入的手机号（根据当前登录模式选择不同的输入框）
        let phone: String?
        if currentLoginMode == .smsCode {
            phone = phoneTextField_code.text
        } else { // .password
            phone = phoneTextField_pwd.text
        }

        // 创建验证手机号页面
        let vmpnVC = VerifyMobilePhoneNumberViewController()
        vmpnVC.verificationType = .bindPwd

        // 如果有手机号，传递给验证页面
        if let phone = phone, isValidPhone(phone) {
            vmpnVC.currentPhoneNumber = phone
        }

        // 1. 如果LoginViewController是在导航控制器中，使用push方式
        if let navigationController = self.navigationController {
            navigationController.pushViewController(vmpnVC, animated: true)
            print("使用push方式导航到验证手机号页面")
            return
        }

        // 2. 如果是通过present显示的，获取presentingViewController
        guard let presenterToUse = self.presentingViewController else {
            // 如果没有presentingViewController，直接present新页面
            vmpnVC.modalPresentationStyle = .fullScreen
            self.present(vmpnVC, animated: true)
            print("无法获取presentingViewController，直接present验证手机号页面")
            return
        }

        // 3. 先关闭当前页面，再由父级呈现新页面
        vmpnVC.modalPresentationStyle = .fullScreen
        self.dismiss(animated: true) {
            presenterToUse.present(vmpnVC, animated: true, completion: nil)
            print("LoginViewController关闭完成，由Presenter弹出验证手机号页面")
        }
    }

    // 辅助方法：验证手机号
    private func isValidPhone(_ phone: String) -> Bool {
        let pattern = "^1[3-9]\\d{9}$"
        let predicate = NSPredicate(format: "SELF MATCHES %@", pattern)
        return predicate.evaluate(with: phone)
    }

    // MARK: - 设置密码处理
    private func navigateToSetPassword(token: String?, phone: String?, checkId: String?) {
        print("准备导航到设置密码页面, Token: \(token ?? "N/A"), Phone: \(phone ?? "N/A"), CheckId: \(checkId ?? "N/A")")
        let setPasswordVC = SetNewPasswordViewController()
        setPasswordVC.phone = phone ?? ""
        setPasswordVC.checkId = checkId ?? ""
        setPasswordVC.isFirstTimeLogin = true
        present(setPasswordVC, animated: true, completion: nil)
    }

    // MARK: - 兴趣选择页面处理
    private func showPreferenceTagsViewController() {
        print("准备显示兴趣选择页面")

        // 在 dismiss 之前捕获 presentingViewController
        guard let presenterToUse = self.presentingViewController else {
            print("错误：在调用 dismiss 之前无法获取到 presentingViewController")
            self.dismiss(animated: true) // 至少关闭自己
            return
        }

        // 创建兴趣选择页面
        let preferenceVC = PreferenceTagsViewController()
        preferenceVC.modalPresentationStyle = .fullScreen

        // 先关闭 LoginViewController，在完成后回调中用捕获到的 presenter 弹出
        self.dismiss(animated: true) {
            print("LoginViewController 关闭完成，由 Presenter: \(presenterToUse) 弹出 PreferenceTagsViewController")
            presenterToUse.present(preferenceVC, animated: true, completion: nil)
        }
    }

    // 处理首次登录完成通知
    @objc private func handleFirstTimeLoginCompleted() {
        print("收到首次登录完成通知，准备显示兴趣选择页面")
        // 只弹一次
        if !isFirstTimeLogin {
            isFirstTimeLogin = true
            showPreferenceTagsViewController()
        }
    }

//    deinit {
//        NotificationCenter.default.removeObserver(self)
//    }

    // 检查是否支持一键登录
    private func checkOneClickLoginSupport() {
        // 调用 LoginHelper 检查是否支持一键登录
        LoginHelper.shared.checkOneClickLoginAvailability { [weak self] isSupported in
            DispatchQueue.main.async {
                self?.pwdSwitchToSmsButton.isHidden = !isSupported
                self?.codeSwitchToPwdButton.isHidden = !isSupported
            }
        }
    }

    // 一键登录按钮点击事件
    @objc private func oneClickLoginTapped() {
        // 如果注入了委托，则交由协调器处理
        if let delegate = delegate {
            delegate.normalLoginDidRequestOneClick(self)
            return
        }
        // --- 兼容旧逻辑 ---
        // 禁用按钮防止重复点击
        pwdSwitchToSmsButton.isEnabled = false
        codeSwitchToPwdButton.isEnabled = false
        
        LoginHelper.shared.presentOneClickLogin(from: self) { [weak self] success, message in
            guard let self = self else { return }
            
            // 恢复按钮状态
            DispatchQueue.main.async {
                self.pwdSwitchToSmsButton.isEnabled = true
                self.codeSwitchToPwdButton.isEnabled = true
            }
            
            if success, let token = message {
                // 一键登录成功，使用 token 进行后续操作
                print("一键登录成功，token: \(token)")
                self.viewModel.loginWithOneClickToken(token: token)
            } else if message == "用户选择其他登录方式" {
                // 用户点击了"切换其他登录方式"按钮；当前页面已具备切换按钮，无需额外处理
                print("用户选择其他登录方式，保持当前 UI，由用户自行点击切换按钮")
            } else {
                // 其他失败情况
                if let errorMsg = message, !errorMsg.contains("用户取消") {
                    DispatchQueue.main.async {
                        self.showToast("一键登录失败: \(errorMsg)")
                    }
                }
                print("一键登录失败或取消: \(message ?? "未知原因")")
            }
        }
    }
}

extension LoginViewController2_0: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        // 如果触摸发生在底部弹窗或其他需要忽略的区域，则不响应
        if let view = touch.view, view.isDescendant(of: bottomSheetView) {
            return false
        }
        return true
    }
}
