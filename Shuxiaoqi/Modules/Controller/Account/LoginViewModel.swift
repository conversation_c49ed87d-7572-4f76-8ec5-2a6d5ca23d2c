//
//  LoginViewModel.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/23.
//

import Foundation
import Combine
import UIKit

// 通知名称扩展
extension Notification.Name {
    static let firstTimeLoginCompleted = Notification.Name("firstTimeLoginCompleted")
}

enum LoginMode {
    case smsCode
    case password
}

class LoginViewModel: ObservableObject {
    // 输入
    @Published var phone: String = ""
    @Published var password: String = ""
    @Published var code: String = ""
    @Published var captcha: String = ""
    @Published var isAgreementChecked: Bool = false
    @Published var loginMode: LoginMode = .smsCode
    @Published var codeId: String = ""
    
    // 输出
    @Published var isLoginButtonEnabled: Bool = false
    @Published var isLoading: Bool = false
    @Published var isCodeButtonEnabled: Bool = true
    @Published var codeButtonTitle: String = "获取验证码"
    @Published var errorMessage: String?
    @Published var loginSuccess: Bool = false
    @Published var showAgreementAlert: Bool = false
    @Published var loginResponse: LoginResponse?
    @Published var showBindPhone: String? = nil
    @Published var showSetPassword: (token: String?, phone: String?, checkId: String?)? = nil
    @Published var showPreferenceTags: Bool = false
    @Published var captchaImage: UIImage? = nil
    @Published var isCaptchaLoading: Bool = false

    // 首次登录标识
    private var isFirstTimeLogin: Bool = false

    private var countdownTimer: Timer?
    private var countdownSeconds = 60
    private var cancellables = Set<AnyCancellable>()

    init() {
        // 输入变化时自动校验
        Publishers.CombineLatest4($phone, $password, $code, $isAgreementChecked)
            .combineLatest($loginMode)
            .sink { [weak self] (inputs, mode) in
                self?.validateInputs(mode: mode, phone: inputs.0, password: inputs.1, code: inputs.2, isAgreementChecked: inputs.3)
            }
            .store(in: &cancellables)

        // 监听首次登录完成通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFirstTimeLoginCompleted),
            name: .firstTimeLoginCompleted,
            object: nil
        )
    }

    // 校验
    private func validateInputs(mode: LoginMode, phone: String, password: String, code: String, isAgreementChecked: Bool) {
        switch mode {
        case .password:
            isLoginButtonEnabled = isAgreementChecked && validatePhone(phone) && !password.isEmpty
        case .smsCode:
            isLoginButtonEnabled = isAgreementChecked && validatePhone(phone) && !code.isEmpty
        }
    }

    func switchLoginMode(_ mode: LoginMode) {
        loginMode = mode
        phone = ""
        password = ""
        code = ""
        captcha = ""
        errorMessage = nil
        stopCountdown()
    }

    func login() {
        guard isAgreementChecked else {
            showAgreementAlert = true
            return
        }
        isLoading = true
        isLoginButtonEnabled = false
        errorMessage = nil
        if loginMode == .smsCode {
            guard validatePhone(phone) else {
                errorMessage = "请输入正确的手机号"
                isLoading = false
                isLoginButtonEnabled = true
                return
            }
            guard !code.isEmpty else {
                errorMessage = "请输入验证码"
                isLoading = false
                isLoginButtonEnabled = true
                return
            }
            // 调用APIManager登录
            APIManager.shared.login(phone: phone, code: code) { [weak self] result in
                DispatchQueue.main.async {
                    self?.isLoading = false
                    self?.isLoginButtonEnabled = true
                    switch result {
                    case .success(let response):
                        self?.handleLoginResponse(response)
                    case .failure(let error):
                        self?.errorMessage = error.errorMessage
                    }
                }
            }
        } else {
            guard validatePhone(phone) else {
                errorMessage = "请输入正确的手机号"
                isLoading = false
                isLoginButtonEnabled = true
                return
            }
            guard !password.isEmpty else {
                errorMessage = "请输入密码"
                isLoading = false
                isLoginButtonEnabled = true
                return
            }
            guard let encryptedPassword = APIManager.shared.encryptPassword(password) else {
                errorMessage = "登录失败，请重试"
                isLoading = false
                isLoginButtonEnabled = true
                return
            }
            APIManager.shared.loginWithPassword(phone: phone, password: encryptedPassword) { [weak self] result in
                DispatchQueue.main.async {
                    self?.isLoading = false
                    self?.isLoginButtonEnabled = true
                    switch result {
                    case .success(let response):
                        self?.handleLoginResponse(response)
                    case .failure(let error):
                        self?.errorMessage = error.errorMessage
                    }
                }
            }
        }
    }

    func getCode() {
        guard validatePhone(phone) else {
            errorMessage = "请输入正确的手机号"
            return
        }
        guard !captcha.isEmpty else {
            errorMessage = "请输入图形验证码"
            return
        }
        isCodeButtonEnabled = false
        codeButtonTitle = "请求中..."
        APIManager.shared.getSMSCode(phone: phone, captchaCode: captcha, codeId: codeId, type: "smsLogin") { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200 {
                        self?.startCountdown()
                    } else {
                        self?.isCodeButtonEnabled = true
                        self?.codeButtonTitle = "获取验证码"
                        self?.errorMessage = response.displayMessage
                    }
                case .failure(let error):
                    self?.isCodeButtonEnabled = true
                    self?.codeButtonTitle = "获取验证码"
                    self?.errorMessage = error.errorMessage
                }
            }
        }
    }

    private func startCountdown() {
        stopCountdown()
        countdownSeconds = 60
        isCodeButtonEnabled = false
        codeButtonTitle = "60s后重试"
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.countdownSeconds -= 1
            if self.countdownSeconds <= 0 {
                self.stopCountdown()
            } else {
                self.codeButtonTitle = "\(self.countdownSeconds)s后重试"
            }
        }
    }

    private func stopCountdown() {
        countdownTimer?.invalidate()
        countdownTimer = nil
        isCodeButtonEnabled = true
        codeButtonTitle = "获取验证码"
    }

    func validatePhone(_ phone: String) -> Bool {
        let pattern = "^1[3-9]\\d{9}$"
        return NSPredicate(format: "SELF MATCHES %@", pattern).evaluate(with: phone)
    }

    private func handleLoginResponse(_ response: LoginResponse) {
        loginResponse = response
        if response.status == 200, let data = response.data {
            switch data.state {
            case 0:
                // 登录成功
                UserDefaults.standard.set(data.tokenValue, forKey: "userToken")
                // 广播 Token 保存事件，供 WebViewController 等监听
                NotificationCenter.default.post(name: .tokenSaved, object: nil, userInfo: ["token": data.tokenValue])
                loginSuccess = true
                // 登录成功后带 Token 再次调用 firstEnter 获取个性化数据
                triggerFirstEnter()
            case 1:
                // 微信注册需绑定手机 - 首次登录
                isFirstTimeLogin = true
                showBindPhone = data.bingPhoneId
            case 2:
                // ✅ 后端已取消“设置密码”流程，直接视为首次登录成功

                // 1. 保存 Token
                UserDefaults.standard.set(data.tokenValue, forKey: "userToken")
                NotificationCenter.default.post(name: .tokenSaved, object: nil, userInfo: ["token": data.tokenValue])

                // 2. 标记首次登录，用于后续兴趣标签弹窗
                isFirstTimeLogin = true

                // 3. 调用 firstEnter 拉取个性化数据
                triggerFirstEnter()

                // 4. 直接触发兴趣标签页面
                showPreferenceTags = true

                // 可选：如需 toast，可同时设置 loginSuccess = true
                // loginSuccess = true
            default:
                errorMessage = "登录遇到未知状态，请稍后重试"
            }
        } else {
            errorMessage = response.displayMessage
        }
    }

    func agreementConfirmed() {
        isAgreementChecked = true
        showAgreementAlert = false
    }

    func resetError() {
        errorMessage = nil
    }

    // 触发兴趣选择页面（在首次登录流程完成后调用）
    func triggerPreferenceTagsIfNeeded() {
        if isFirstTimeLogin {
            showPreferenceTags = true
            isFirstTimeLogin = false // 重置标识
        }
    }

    // 处理首次登录完成通知
    @objc private func handleFirstTimeLoginCompleted() {
        DispatchQueue.main.async { [weak self] in
            self?.triggerPreferenceTagsIfNeeded()
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    func refreshCaptcha() {
        isCaptchaLoading = true
        captchaImage = nil
        // 生成新的codeId
        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let random1 = Int.random(in: 0...9)
        let random2 = Int.random(in: 0...9)
        codeId = "\(timestamp)\(random1)\(random2)"
        APIManager.shared.getCaptcha(codeId: codeId, type: "smsLogin") { [weak self] result in
            DispatchQueue.main.async {
                self?.isCaptchaLoading = false
                switch result {
                case .success(let image):
                    if let img = image {
                        self?.captchaImage = img
                    } else {
                        self?.errorMessage = "无法显示验证码图片"
                    }
                case .failure(let error):
                    self?.errorMessage = error.errorMessage
                }
            }
        }
    }

    // 微信登录
    func loginWithWeChat(code: String) {
        print("获取到的微信code: \(code)")
        isLoading = true
        isLoginButtonEnabled = false
        errorMessage = nil
        APIManager.shared.loginWithWeChat(code: code) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false
                self?.isLoginButtonEnabled = true
                switch result {
                case .success(let response):
                    self?.handleLoginResponse(response)
                case .failure(let error):
                    self?.errorMessage = error.errorMessage
                }
            }
        }
    }
    
    // 一键登录
    func loginWithOneClickToken(token: String) {
        print("获取到的一键登录token: \(token)")
        isLoading = true
        isLoginButtonEnabled = false
        errorMessage = nil

        // 调用一键登录API
        APIManager.shared.loginWithOneClickToken(token: token) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false
                self?.isLoginButtonEnabled = true
                switch result {
                case .success(let response):
                    self?.handleLoginResponse(response)
                case .failure(let error):
                    self?.errorMessage = error.errorMessage
                }
            }
        }
    }

    // 苹果登录
    func loginWithApple(identityToken: String) {
        print("获取到的苹果登录identityToken: \(identityToken)")
        isLoading = true
        isLoginButtonEnabled = false
        errorMessage = nil

        // 调用苹果登录API
        APIManager.shared.loginWithApple(identityToken: identityToken) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false
                self?.isLoginButtonEnabled = true
                switch result {
                case .success(let response):
                    self?.handleLoginResponse(response)
                case .failure(let error):
                    self?.errorMessage = error.errorMessage
                }
            }
        }
    }

    /// 登录或自动登录后，带 Token 调用 firstEnter 拉取个性化启动数据
    private func triggerFirstEnter() {
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
        let deviceName = DeviceUtils.marketingDeviceModel()
        let deviceSystem = UIDevice.current.systemVersion
        let deviceType = 1

        // 获取本地IP
        let localIP = DeviceUtils.getCurrentIP()

        // 尝试获取位置信息
        LocationManager.shared.getCurrentAreaCode { areaCode, address in
            let loginAddress = address ?? ""

            // 获取公网IP
            DeviceUtils.getPublicIP { publicIP in
                let finalIP = publicIP ?? localIP

                // 更新并保存当前设备信息
                let currentDeviceInfo = DeviceUtils.CurrentDeviceInfo(
                    deviceId: deviceId,
                    deviceName: deviceName,
                    deviceSystem: deviceSystem,
                    deviceType: deviceType,
                    ip: finalIP,
                    location: loginAddress
                )
                DeviceUtils.saveCurrentDeviceInfo(currentDeviceInfo)

                // 调用firstEnter接口
                APIManager.shared.firstEnter(
                    deviceId: deviceId,
                    deviceName: deviceName,
                    deviceSystem: deviceSystem,
                    deviceType: deviceType,
                    loginAddress: loginAddress
                ) { result in
                    switch result {
                    case .success(let resp):
                        print("[firstEnter-afterLogin] success: \(resp.displayMessage)")
                    case .failure(let err):
                        print("[firstEnter-afterLogin] failed: \(err)")
                    }
                }
            }
        }
    }
}
